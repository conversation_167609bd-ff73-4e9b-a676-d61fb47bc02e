# Doctor Chat Test Guide

## 🎯 **What We Fixed**

### 1. **Integrated useChat Hook**
- Doctor chat now uses the same `useChat` hook as student chat
- Proper message loading and sending
- Real-time message updates

### 2. **Fixed Message Structure**
- Updated message rendering to use correct fields:
  - `message.content` instead of `message.text`
  - `message.senderRole` instead of `message.sender`
  - `message.timestamp` instead of `message.time`

### 3. **Added Debug Function**
- `window.debugDoctorChat()` - Check doctor chat state

## 🧪 **Test Steps**

### Step 1: Test as Doctor
1. **Login as doctor**
2. **Go to <PERSON> page** (`/doctor/chat`)
3. **Open browser console** (F12)
4. **Run debug command**:
   ```javascript
   window.debugDoctorChat()
   ```

### Step 2: Check Console Output
You should see:
```javascript
{
  doctorId: "doctor_uid_here",
  conversationsCount: 1, // or more
  patientsCount: 1, // or more
  selectedPatient: null, // initially
  selectedConversation: null, // initially
  messagesCount: 0 // initially
}
```

### Step 3: Test Patient List
1. **Check if patients appear** in the left sidebar
2. **Click on a patient** to select them
3. **Run debug again** to see if conversation is selected:
   ```javascript
   window.debugDoctorChat()
   ```

### Step 4: Test Messaging
1. **Select a patient**
2. **Type a message** in the input field
3. **Send the message**
4. **Check if message appears** in the chat area

## 🔍 **Expected Behavior**

### When Working Correctly:
1. **Doctor sees patient list** with students who have active conversations
2. **Clicking patient** loads the conversation and messages
3. **Messages display properly** with correct styling
4. **Sending messages works** and appears in real-time
5. **Student receives messages** on their end

### Console Logs to Look For:
```
📋 Doctor has X patients with conversations
📋 Conversations for doctor: [array of conversations]
📋 Patient list: [array of patients]
📱 Selected conversation with PatientName: conversationId
✅ Message sent successfully
```

## 🚨 **Common Issues & Solutions**

### Issue 1: No Patients in List
**Cause**: No active conversations exist
**Solution**: 
1. Have a student send a connection request
2. Accept the request as doctor
3. Student should start a conversation
4. Refresh doctor chat page

### Issue 2: Messages Not Loading
**Cause**: useChat hook not working properly
**Solution**: Check console for errors and verify conversation selection

### Issue 3: Can't Send Messages
**Cause**: sendMessage function not working
**Solution**: Check if selectedConversation is properly set

## 🔧 **Manual Test Scenario**

### Complete End-to-End Test:
1. **Student Side**:
   - Login as student
   - Send connection request to doctor
   - Wait for acceptance

2. **Doctor Side**:
   - Login as doctor
   - Accept student's request
   - Go to chat page

3. **Student Side**:
   - Click on connected doctor
   - Send a message: "Hello Doctor!"

4. **Doctor Side**:
   - Refresh chat page if needed
   - Should see student in patient list
   - Click on student
   - Should see "Hello Doctor!" message
   - Reply: "Hello! How can I help you?"

5. **Student Side**:
   - Should see doctor's reply in real-time

## ✅ **Success Indicators**

- ✅ Doctor sees patients with active conversations
- ✅ Clicking patient loads conversation
- ✅ Messages display with proper formatting
- ✅ Sending messages works without errors
- ✅ Real-time message updates
- ✅ No console errors

## 📞 **Debug Commands**

```javascript
// Check doctor chat state
window.debugDoctorChat()

// Check conversations
console.log('Conversations:', window.conversations)

// Check current user
console.log('Doctor profile:', window.userProfile)

// Check selected conversation
console.log('Selected conversation:', window.selectedConversation)
```

Run these tests and let me know what you see! The doctor should now be able to see and reply to student messages properly.
