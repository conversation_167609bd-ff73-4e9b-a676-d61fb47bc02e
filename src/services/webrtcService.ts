import { db } from './firebase';
import { doc, setDoc, onSnapshot, updateDoc, deleteDoc, collection, addDoc } from 'firebase/firestore';

export interface CallSession {
  id: string;
  callerId: string;
  calleeId: string;
  type: 'video' | 'voice';
  status: 'initiating' | 'ringing' | 'active' | 'ended' | 'declined';
  offer?: RTCSessionDescriptionInit;
  answer?: RTCSessionDescriptionInit;
  iceCandidates: RTCIceCandidateInit[];
  createdAt: Date;
}

class WebRTCService {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private callSessionId: string | null = null;
  private onRemoteStreamCallback: ((stream: MediaStream) => void) | null = null;
  private onCallEndCallback: (() => void) | null = null;

  private readonly iceServers = [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' }
  ];

  constructor() {
    this.setupPeerConnection();
  }

  private setupPeerConnection() {
    this.peerConnection = new RTCPeerConnection({
      iceServers: this.iceServers
    });

    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.callSessionId) {
        this.addIceCandidate(this.callSessionId, event.candidate);
      }
    };

    this.peerConnection.ontrack = (event) => {
      this.remoteStream = event.streams[0];
      if (this.onRemoteStreamCallback) {
        this.onRemoteStreamCallback(this.remoteStream);
      }
    };

    this.peerConnection.onconnectionstatechange = () => {
      if (this.peerConnection?.connectionState === 'disconnected' || 
          this.peerConnection?.connectionState === 'failed') {
        this.endCall();
      }
    };
  }

  async initializeCall(calleeId: string, type: 'video' | 'voice', callerId: string): Promise<string> {
    try {
      // Get user media
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: type === 'video',
        audio: true
      });

      // Add tracks to peer connection
      this.localStream.getTracks().forEach(track => {
        if (this.peerConnection && this.localStream) {
          this.peerConnection.addTrack(track, this.localStream);
        }
      });

      // Create call session in Firestore
      const callRef = doc(collection(db, 'calls'));
      this.callSessionId = callRef.id;

      await setDoc(callRef, {
        id: this.callSessionId,
        callerId,
        calleeId,
        type,
        status: 'initiating',
        iceCandidates: [],
        createdAt: new Date()
      });

      // Create offer
      const offer = await this.peerConnection!.createOffer();
      await this.peerConnection!.setLocalDescription(offer);

      // Save offer to Firestore
      await updateDoc(callRef, {
        offer: offer,
        status: 'ringing'
      });

      // Listen for answer
      this.listenForCallUpdates(this.callSessionId);

      return this.callSessionId;
    } catch (error) {
      console.error('Error initializing call:', error);
      throw error;
    }
  }

  async answerCall(callSessionId: string): Promise<void> {
    try {
      this.callSessionId = callSessionId;
      
      // Get user media
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: true, // You can make this dynamic based on call type
        audio: true
      });

      // Add tracks to peer connection
      this.localStream.getTracks().forEach(track => {
        if (this.peerConnection && this.localStream) {
          this.peerConnection.addTrack(track, this.localStream);
        }
      });

      // Get call data from Firestore
      const callRef = doc(db, 'calls', callSessionId);
      
      // Listen for call updates
      this.listenForCallUpdates(callSessionId);

      // Set remote description from offer
      const callDoc = await import('firebase/firestore').then(({ getDoc }) => getDoc(callRef));
      if (callDoc.exists()) {
        const callData = callDoc.data();
        if (callData.offer) {
          await this.peerConnection!.setRemoteDescription(callData.offer);
          
          // Create answer
          const answer = await this.peerConnection!.createAnswer();
          await this.peerConnection!.setLocalDescription(answer);
          
          // Save answer to Firestore
          await updateDoc(callRef, {
            answer: answer,
            status: 'active'
          });
        }
      }
    } catch (error) {
      console.error('Error answering call:', error);
      throw error;
    }
  }

  async declineCall(callSessionId: string): Promise<void> {
    const callRef = doc(db, 'calls', callSessionId);
    await updateDoc(callRef, {
      status: 'declined'
    });
  }

  async endCall(): Promise<void> {
    try {
      // Stop local stream
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => track.stop());
        this.localStream = null;
      }

      // Close peer connection
      if (this.peerConnection) {
        this.peerConnection.close();
        this.setupPeerConnection(); // Reset for next call
      }

      // Update call status in Firestore
      if (this.callSessionId) {
        const callRef = doc(db, 'calls', this.callSessionId);
        await updateDoc(callRef, {
          status: 'ended'
        });
        
        // Clean up call document after a delay
        setTimeout(async () => {
          try {
            await deleteDoc(callRef);
          } catch (error) {
            console.error('Error cleaning up call document:', error);
          }
        }, 5000);
      }

      // Reset state
      this.callSessionId = null;
      this.remoteStream = null;

      // Notify callback
      if (this.onCallEndCallback) {
        this.onCallEndCallback();
      }
    } catch (error) {
      console.error('Error ending call:', error);
    }
  }

  private async addIceCandidate(callSessionId: string, candidate: RTCIceCandidate): Promise<void> {
    try {
      const callRef = doc(db, 'calls', callSessionId);
      const candidateRef = doc(collection(callRef, 'iceCandidates'));
      await setDoc(candidateRef, candidate.toJSON());
    } catch (error) {
      console.error('Error adding ICE candidate:', error);
    }
  }

  private listenForCallUpdates(callSessionId: string): void {
    const callRef = doc(db, 'calls', callSessionId);
    
    onSnapshot(callRef, async (doc) => {
      if (doc.exists()) {
        const callData = doc.data();
        
        // Handle answer
        if (callData.answer && !this.peerConnection?.remoteDescription) {
          await this.peerConnection?.setRemoteDescription(callData.answer);
        }
        
        // Handle call end
        if (callData.status === 'ended' || callData.status === 'declined') {
          this.endCall();
        }
      }
    });

    // Listen for ICE candidates
    const iceCandidatesRef = collection(callRef, 'iceCandidates');
    onSnapshot(iceCandidatesRef, (snapshot) => {
      snapshot.docChanges().forEach(async (change) => {
        if (change.type === 'added') {
          const candidateData = change.doc.data();
          const candidate = new RTCIceCandidate(candidateData);
          try {
            await this.peerConnection?.addIceCandidate(candidate);
          } catch (error) {
            console.error('Error adding ICE candidate:', error);
          }
        }
      });
    });
  }

  toggleMute(): boolean {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        return !audioTrack.enabled; // Return muted state
      }
    }
    return false;
  }

  toggleVideo(): boolean {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled;
        return !videoTrack.enabled; // Return video disabled state
      }
    }
    return false;
  }

  getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  getRemoteStream(): MediaStream | null {
    return this.remoteStream;
  }

  setOnRemoteStreamCallback(callback: (stream: MediaStream) => void): void {
    this.onRemoteStreamCallback = callback;
  }

  setOnCallEndCallback(callback: () => void): void {
    this.onCallEndCallback = callback;
  }

  // Listen for incoming calls
  listenForIncomingCalls(userId: string, callback: (callData: any) => void): () => void {
    const callsRef = collection(db, 'calls');
    return onSnapshot(callsRef, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          const callData = change.doc.data();
          if (callData.calleeId === userId && callData.status === 'ringing') {
            callback({ id: change.doc.id, ...callData });
          }
        }
      });
    });
  }
}

export const webrtcService = new WebRTCService();
export default webrtcService;
