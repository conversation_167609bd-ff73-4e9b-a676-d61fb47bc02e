import React, { useState, useEffect, useRef } from 'react';
import {
  Container, Typography, Box, Grid, Paper, Button,
  List, ListItem, ListItemText, Avatar, Divider,
  TextField, InputAdornment, Chip, Card, CardContent,
  IconButton, Badge, Dialog, DialogTitle, DialogContent,
  DialogActions, Tab, Tabs, CircularProgress
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import Layout from '../../components/layout/Layout';
import SearchIcon from '@mui/icons-material/Search';
import SendIcon from '@mui/icons-material/Send';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import VideoCallIcon from '@mui/icons-material/VideoCall';
import PhoneIcon from '@mui/icons-material/Phone';
import CallEndIcon from '@mui/icons-material/CallEnd';
import MicIcon from '@mui/icons-material/Mic';
import MicOffIcon from '@mui/icons-material/MicOff';
import VideocamIcon from '@mui/icons-material/Videocam';
import VideocamOffIcon from '@mui/icons-material/VideocamOff';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import { useFirebase } from '../../contexts/FirebaseContext';
import chatService from '../../services/chatService';
import type { Conversation, Message, UserPresence } from '../../types/chat';
import { useAuth } from '../../contexts/AuthContext';
import { useChat } from '../../hooks/useChat';
import webrtcService from '../../services/webrtcService';

// Add CSS animations for the voice call overlay
const callAnimations =
`
  @keyframes pulse {
    0%, 100% { opacity: 0.6; }
    50% { opacity: 1; }
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes avatarPulse {
    0%, 100% { transform: scale(1); box-shadow: 0 8px 32px rgba(0,0,0,0.3); }
    50% { transform: scale(1.05); box-shadow: 0 12px 40px rgba(0,0,0,0.4); }
  }

  @keyframes soundWave {
    0% { opacity: 1; transform: translate(-50%, -50%) scale(0.8); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(1.2); }
  }

  @keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
  }
`;

// Inject animations into the document
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = callAnimations;
  document.head.appendChild(styleSheet);
}

const DoctorChat = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useFirebase();
  const { userProfile } = useAuth();

  // Use the chat hook for proper message handling
  const {
    conversations,
    selectedConversation,
    messages,
    loading: chatLoading,
    sending,
    selectConversation,
    sendMessage,
    handleTyping,
    getOtherParticipant
  } = useChat();

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPatient, setSelectedPatient] = useState<any>(null);
  const [patients, setPatients] = useState<any[]>([]); // Will be derived from conversations
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [isVideoCallActive, setIsVideoCallActive] = useState(false);
  const [isVoiceCallActive, setIsVoiceCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [presence, setPresence] = useState<UserPresence[]>([]);
  const [currentCallId, setCurrentCallId] = useState<string | null>(null);
  const [localVideoRef, setLocalVideoRef] = useState<HTMLVideoElement | null>(null);
  const [remoteVideoRef, setRemoteVideoRef] = useState<HTMLVideoElement | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Map conversations to patient list
  useEffect(() => {
    if (!userProfile?.uid || !conversations.length) {
      setPatients([]);
      return;
    }

    const patientList = conversations.map(conv => {
      // Find the patient participant (not the doctor)
      const patientId = conv.participants.find(id => id !== userProfile.uid);
      const details = conv.participantDetails[patientId];

      if (!patientId || !details) {
        console.warn('Invalid conversation data:', conv);
        return null;
      }

      return {
        id: patientId,
        name: details.name,
        studentId: patientId,
        lastMessage: conv.lastMessage?.content || '',
        lastMessageTime: conv.lastMessage?.timestamp?.toDate?.().toLocaleString() || '',
        unreadCount: conv.unreadCounts?.[userProfile.uid] || 0,
        online: false, // Will update with presence
        avatar: details.avatar || '',
        conversationId: conv.id
      };
    }).filter(Boolean); // Remove null entries

    setPatients(patientList);
    console.log(`📋 Doctor has ${patientList.length} patients with conversations`);
    console.log('📋 Conversations for doctor:', conversations.map(c => ({
      id: c.id,
      participants: c.participants,
      lastMessage: c.lastMessage?.content
    })));
    console.log('📋 Patient list:', patientList.map(p => ({
      name: p.name,
      id: p.id,
      conversationId: p.conversationId
    })));
  }, [conversations, userProfile?.uid]);

  // Auto-scroll to bottom when new messages arrive (using useChat hook messages)
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Update presence status of patients based on real-time updates
  useEffect(() => {
    const updatePresence = (presences: UserPresence[]) => {
      setPresence(presences);
      // Update patient online status
      setPatients(prevPatients =>
        prevPatients.map(patient => {
          const presenceData = presences.find(p => p.userId === patient.id);
          return {
            ...patient,
            online: presenceData ? presenceData.status === 'online' : false
          };
        })
      );
    };

    if (!patients.length) return;
    const patientIds = patients.map(p => p.id).filter(Boolean);
    if (!patientIds.length) return; // Prevent empty array error
    const unsubscribe = chatService.subscribeToPresence(patientIds, setPresence);
    return () => unsubscribe();
  }, [patients]);

  useEffect(() => {
    // Check if a patient was passed from another page
    if (location.state?.selectedPatient) {
      const patient = patients.find(p => p.name === location.state.selectedPatient.name);
      if (patient) {
        handleSelectPatient(patient);
      }
    }
  }, [location.state, patients]);

  // Debug function for doctor chat
  const debugDoctorChat = () => {
    console.log('🩺 DOCTOR CHAT DEBUG');
    console.log('Doctor ID:', userProfile?.uid);
    console.log('Current User:', currentUser?.uid);
    console.log('Conversations:', conversations.length);
    console.log('Patients:', patients.length);
    console.log('Selected Patient:', selectedPatient?.name);
    console.log('Selected Conversation:', selectedConversation?.id);
    console.log('Messages:', messages.length);
    console.log('Chat Loading:', chatLoading);
    console.log('Conversations Data:', conversations);

    return {
      doctorId: userProfile?.uid,
      currentUserId: currentUser?.uid,
      conversationsCount: conversations.length,
      patientsCount: patients.length,
      selectedPatient: selectedPatient?.name,
      selectedConversation: selectedConversation?.id,
      messagesCount: messages.length,
      chatLoading
    };
  };

  // Auto-debug when component mounts or data changes
  useEffect(() => {
    if (userProfile?.uid) {
      debugDoctorChat();
    }
  }, [userProfile?.uid, conversations.length, patients.length, messages.length, chatLoading]);

  // Set up video streams when call becomes active
  useEffect(() => {
    if (isVideoCallActive && localVideoRef) {
      const localStream = webrtcService.getLocalStream();
      if (localStream) {
        localVideoRef.srcObject = localStream;
      }
    }
  }, [isVideoCallActive, localVideoRef]);

  // Listen for incoming calls
  useEffect(() => {
    if (!userProfile?.uid) return;

    const unsubscribe = webrtcService.listenForIncomingCalls(userProfile.uid, (callData) => {
      // Handle incoming call notification
      const accept = window.confirm(`Incoming ${callData.type} call from a patient. Accept?`);
      if (accept) {
        webrtcService.answerCall(callData.id);
        if (callData.type === 'video') {
          setIsVideoCallActive(true);
        } else {
          setIsVoiceCallActive(true);
        }
        setCurrentCallId(callData.id);
      } else {
        webrtcService.declineCall(callData.id);
      }
    });

    return unsubscribe;
  }, [userProfile?.uid]);

  // Test function to create a sample conversation
  const createTestConversation = async () => {
    if (!userProfile?.uid) {
      alert('Doctor profile not loaded');
      return;
    }

    try {
      // Create a test student user document first
      const testStudentId = 'test-student-123';
      const { doc, setDoc } = await import('firebase/firestore');
      const { db } = await import('../services/firebase');

      // Create test student profile
      await setDoc(doc(db, 'users', testStudentId), {
        displayName: 'Test Student',
        email: '<EMAIL>',
        role: 'user',
        department: 'Computer Science',
        phone: '+**********',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      console.log('✅ Test student profile created');

      // Create a test conversation with the mock student
      const conversationId = await chatService.createConversation(testStudentId, userProfile.uid);

      // Send a test message
      await chatService.sendMessage(conversationId, testStudentId, 'Hello Doctor, I need help with my health condition.');

      console.log('✅ Test conversation created:', conversationId);
      alert('Test conversation created successfully! Check the patient list.');
    } catch (error) {
      console.error('❌ Error creating test conversation:', error);
      alert('Failed to create test conversation: ' + error.message);
    }
  };

  // Make debug function available globally
  if (typeof window !== 'undefined') {
    (window as any).debugDoctorChat = debugDoctorChat;
    (window as any).createTestConversation = createTestConversation;
  }

  const handleSelectPatient = (patient: any) => {
    setSelectedPatient(patient);

    // Find the conversation with this patient
    const conversation = conversations.find(conv =>
      conv.participants.includes(patient.id) && conv.participants.includes(userProfile?.uid)
    );

    if (conversation) {
      // Use the useChat hook's selectConversation function
      selectConversation(conversation);
      console.log(`📱 Selected conversation with ${patient.name}:`, conversation.id);
    } else {
      console.warn(`No conversation found with patient ${patient.name}`);
    }

    // Mark messages as read
    setPatients(prev =>
      prev.map(p =>
        p.id === patient.id ? { ...p, unreadCount: 0 } : p
      )
    );
  };

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedConversation) return;

    try {
      // Use the useChat hook's sendMessage function
      await sendMessage(newMessage.trim());
      setNewMessage('');
      console.log('✅ Message sent successfully');
    } catch (error) {
      console.error('❌ Failed to send message:', error);
      alert('Failed to send message. Please try again.');
    }
  };

  const handleStartVideoCall = async () => {
    if (!selectedPatient || !userProfile?.uid) return;

    try {
      const callId = await webrtcService.initializeCall(selectedPatient.id, 'video', userProfile.uid);
      setCurrentCallId(callId);
      setIsVideoCallActive(true);
      setIsVoiceCallActive(false);

      // Set up video streams
      const localStream = webrtcService.getLocalStream();
      if (localStream && localVideoRef) {
        localVideoRef.srcObject = localStream;
      }

      webrtcService.setOnRemoteStreamCallback((remoteStream) => {
        if (remoteVideoRef) {
          remoteVideoRef.srcObject = remoteStream;
        }
      });

      webrtcService.setOnCallEndCallback(() => {
        handleEndCall();
      });
    } catch (error) {
      console.error('Error starting video call:', error);
      alert('Failed to start video call. Please check your camera and microphone permissions.');
    }
  };

  const handleStartVoiceCall = async () => {
    if (!selectedPatient || !userProfile?.uid) return;

    try {
      const callId = await webrtcService.initializeCall(selectedPatient.id, 'voice', userProfile.uid);
      setCurrentCallId(callId);
      setIsVoiceCallActive(true);
      setIsVideoCallActive(false);

      webrtcService.setOnCallEndCallback(() => {
        handleEndCall();
      });
    } catch (error) {
      console.error('Error starting voice call:', error);
      alert('Failed to start voice call. Please check your microphone permissions.');
    }
  };

  const handleEndCall = async () => {
    try {
      await webrtcService.endCall();
    } catch (error) {
      console.error('Error ending call:', error);
    } finally {
      setIsVideoCallActive(false);
      setIsVoiceCallActive(false);
      setIsMuted(false);
      setIsVideoEnabled(true);
      setCurrentCallId(null);

      // Clear video streams
      if (localVideoRef) {
        localVideoRef.srcObject = null;
      }
      if (remoteVideoRef) {
        remoteVideoRef.srcObject = null;
      }
    }
  };

  const toggleMute = () => {
    const newMutedState = webrtcService.toggleMute();
    setIsMuted(newMutedState);
  };

  const toggleVideo = () => {
    const newVideoDisabledState = webrtcService.toggleVideo();
    setIsVideoEnabled(!newVideoDisabledState);
  };

  return (
    <Layout>
      <Box sx={{
        bgcolor: '#f5f7fa',
        minHeight: '100vh',
        py: 4
      }}>
        <Container maxWidth="lg">
          {/* Header */}
          <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                Doctor Chat Center
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Communicate with your patients through secure messaging
              </Typography>
            </Box>
            <Box>
              <Button
                variant="outlined"
                size="small"
                onClick={debugDoctorChat}
                sx={{ mr: 2 }}
              >
                Debug Chat
              </Button>
              <Button
                variant="outlined"
                size="small"
                onClick={() => {
                  console.log('🔄 Manually refreshing conversations...');
                  if (currentUser?.uid) {
                    chatService.getConversations(currentUser.uid).then(convs => {
                      console.log('📋 Manual fetch result:', convs);
                    });
                  }
                }}
                sx={{ mr: 1 }}
              >
                Refresh
              </Button>
              <Button
                variant="contained"
                size="small"
                onClick={createTestConversation}
                color="secondary"
              >
                Create Test Chat
              </Button>
            </Box>
          </Box>

          <Grid container spacing={3}>
            {/* Patient List */}
            <Grid item xs={12} md={4}>
              <Paper sx={{
                p: 2,
                height: '70vh',
                maxHeight: '70vh',
                borderRadius: 3,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden'
              }}>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Patients
                </Typography>
                <Divider sx={{ mb: 2 }} />
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Select a patient to start a conversation
                </Typography>
                <TextField
                  fullWidth
                  size="small"
                  placeholder="Search patients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2
                    }
                  }}
                />
                <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
                  {chatLoading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 4 }}>
                      <CircularProgress size={24} />
                      <Typography variant="body2" sx={{ ml: 2 }}>Loading conversations...</Typography>
                    </Box>
                  ) : (
                    <List>
                      {patients.filter(patient =>
                        patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        patient.studentId.toLowerCase().includes(searchTerm.toLowerCase())
                      ).length === 0 ? (
                        <Box sx={{ textAlign: 'center', py: 4, px: 2 }}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            {conversations.length === 0
                              ? "No conversations yet. Patients will appear here when they start a chat with you."
                              : "No patients match your search."
                            }
                          </Typography>
                          {conversations.length === 0 && (
                            <Typography variant="caption" color="text.secondary">
                              Debug: Doctor ID: {userProfile?.uid || 'Not loaded'}
                            </Typography>
                          )}
                        </Box>
                      ) : (
                        patients.filter(patient =>
                          patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          patient.studentId.toLowerCase().includes(searchTerm.toLowerCase())
                        ).map((patient) => (
                      <ListItem
                        key={patient.id}
                        button
                        selected={selectedPatient?.id === patient.id}
                        onClick={() => handleSelectPatient(patient)}
                        sx={{
                          borderRadius: 2,
                          mb: 1,
                          '&.Mui-selected': {
                            backgroundColor: 'primary.main',
                            color: 'white',
                            '&:hover': {
                              backgroundColor: 'primary.main'
                            },
                            '& .MuiListItemText-primary': {
                              color: 'white !important'
                            },
                            '& .MuiListItemText-secondary': {
                              color: 'white !important'
                            },
                            '& .MuiListItemText-secondary *': {
                              color: 'white !important'
                            },
                            '& .MuiSvgIcon-root': {
                              color: 'white !important'
                            }
                          },
                          '&:hover': {
                            backgroundColor: 'rgba(0, 114, 255, 0.08)'
                          }
                        }}
                      >
                        <Avatar
                          alt={patient.name}
                          src={patient.avatar}
                          sx={{ mr: 2 }}
                        >
                          {patient.name.charAt(0)}
                        </Avatar>
                        <ListItemText
                          primary={patient.name}
                          secondary={
                            <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                              <Typography variant="body2" component="span" noWrap>
                                {patient.lastMessage}
                              </Typography>
                              <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                                <FiberManualRecordIcon
                                  sx={{
                                    color: patient.online ? 'success.main' : 'text.disabled',
                                    fontSize: 8,
                                    mr: 0.5
                                  }}
                                />
                                <Typography variant="caption" component="span">
                                  {patient.online ? 'Online' : 'Offline'}
                                </Typography>
                              </Box>
                              {patient.unreadCount > 0 && (
                                <Badge badgeContent={patient.unreadCount} color="error" sx={{ ml: 2, pl: 1 }} />
                              )}
                            </Box>
                          }
                          primaryTypographyProps={{
                            fontWeight: selectedPatient?.id === patient.id ? 'bold' : 'medium'
                          }}
                        />
                      </ListItem>
                        ))
                      )}
                    </List>
                  )}
                </Box>
              </Paper>
            </Grid>

            {/* Chat Area */}
            <Grid item xs={12} md={8}>
              <Paper sx={{
                p: 0,
                height: '70vh',
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 3,
                overflow: 'hidden',
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                position: 'relative'
              }}>
                {selectedPatient ? (
                  <>
                    {/* Chat Header */}
                    <Box sx={{
                      p: 2,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar
                          alt={selectedPatient.name}
                          src={selectedPatient.avatar}
                          sx={{ mr: 2 }}
                        >
                          {selectedPatient.name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="subtitle1" fontWeight="bold">
                            {selectedPatient.name}
                          </Typography>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <FiberManualRecordIcon
                              sx={{
                                color: selectedPatient.online ? 'success.light' : 'text.disabled',
                                fontSize: 12,
                                mr: 0.5
                              }}
                            />
                            <Typography variant="caption">
                              {selectedPatient.online ? 'Online' : 'Offline'}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                      <Box>
                        <IconButton
                          color="inherit"
                          size="small"
                          sx={{ mr: 1 }}
                          onClick={handleStartVoiceCall}
                          disabled={isVideoCallActive || isVoiceCallActive}
                        >
                          <PhoneIcon />
                        </IconButton>
                        <IconButton
                          color="inherit"
                          size="small"
                          onClick={handleStartVideoCall}
                          disabled={isVideoCallActive || isVoiceCallActive}
                        >
                          <VideoCallIcon />
                        </IconButton>
                      </Box>
                    </Box>

                    {/* Video/Voice Call Overlay */}
                    {(isVideoCallActive || isVoiceCallActive) && (
                      <Box sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        background: isVideoCallActive
                          ? 'rgba(0,0,0,0.95)'
                          : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        zIndex: 1000,
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        borderRadius: 3,
                        overflow: 'hidden',
                        '&::before': isVoiceCallActive ? {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          background: 'radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%)',
                          animation: 'pulse 3s ease-in-out infinite'
                        } : {}
                      }}>
                        {/* Animated Background Elements for Voice Call */}
                        {isVoiceCallActive && (
                          <>
                            <Box sx={{
                              position: 'absolute',
                              top: '20%',
                              left: '10%',
                              width: 60,
                              height: 60,
                              borderRadius: '50%',
                              background: 'rgba(255,255,255,0.1)',
                              animation: 'float 6s ease-in-out infinite'
                            }} />
                            <Box sx={{
                              position: 'absolute',
                              top: '60%',
                              right: '15%',
                              width: 40,
                              height: 40,
                              borderRadius: '50%',
                              background: 'rgba(255,255,255,0.08)',
                              animation: 'float 4s ease-in-out infinite reverse'
                            }} />
                            <Box sx={{
                              position: 'absolute',
                              bottom: '20%',
                              left: '20%',
                              width: 80,
                              height: 80,
                              borderRadius: '50%',
                              background: 'rgba(255,255,255,0.05)',
                              animation: 'float 8s ease-in-out infinite'
                            }} />
                          </>
                        )}

                        {/* Call Status */}
                        <Box sx={{ textAlign: 'center', mb: 4, zIndex: 1 }}>
                          {/* Patient Avatar with Pulse Animation for Voice Call */}
                          <Box sx={{ position: 'relative', display: 'inline-block', mb: 3 }}>
                            <Avatar sx={{
                              width: 140,
                              height: 140,
                              bgcolor: 'rgba(255,255,255,0.2)',
                              border: '4px solid rgba(255,255,255,0.3)',
                              fontSize: '3.5rem',
                              boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
                              animation: isVoiceCallActive ? 'avatarPulse 2s ease-in-out infinite' : 'none'
                            }}>
                              {selectedPatient.name.charAt(0)}
                            </Avatar>

                            {/* Voice Call Sound Waves */}
                            {isVoiceCallActive && (
                              <>
                                <Box sx={{
                                  position: 'absolute',
                                  top: '50%',
                                  left: '50%',
                                  transform: 'translate(-50%, -50%)',
                                  width: 180,
                                  height: 180,
                                  border: '2px solid rgba(255,255,255,0.3)',
                                  borderRadius: '50%',
                                  animation: 'soundWave 2s ease-out infinite'
                                }} />
                                <Box sx={{
                                  position: 'absolute',
                                  top: '50%',
                                  left: '50%',
                                  transform: 'translate(-50%, -50%)',
                                  width: 220,
                                  height: 220,
                                  border: '2px solid rgba(255,255,255,0.2)',
                                  borderRadius: '50%',
                                  animation: 'soundWave 2s ease-out infinite 0.5s'
                                }} />
                                <Box sx={{
                                  position: 'absolute',
                                  top: '50%',
                                  left: '50%',
                                  transform: 'translate(-50%, -50%)',
                                  width: 260,
                                  height: 260,
                                  border: '2px solid rgba(255,255,255,0.1)',
                                  borderRadius: '50%',
                                  animation: 'soundWave 2s ease-out infinite 1s'
                                }} />
                              </>
                            )}
                          </Box>

                          <Typography variant="h4" fontWeight="bold" gutterBottom sx={{
                            textShadow: '0 2px 10px rgba(0,0,0,0.3)',
                            mb: 1
                          }}>
                            {selectedPatient.name}
                          </Typography>

                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            mb: 2,
                            gap: 1
                          }}>
                            <Box sx={{
                              width: 12,
                              height: 12,
                              borderRadius: '50%',
                              bgcolor: 'success.light',
                              animation: 'blink 1.5s ease-in-out infinite'
                            }} />
                            <Typography variant="h6" sx={{
                              opacity: 0.9,
                              fontWeight: 500,
                              textShadow: '0 1px 5px rgba(0,0,0,0.2)'
                            }}>
                              {isVideoCallActive ? '📹 Video Call' : '🎙️ Voice Call'}
                            </Typography>
                          </Box>

                          <Typography variant="body1" sx={{
                            opacity: 0.8,
                            fontSize: '1.1rem',
                            textShadow: '0 1px 3px rgba(0,0,0,0.2)'
                          }}>
                            Connected and secure
                          </Typography>
                        </Box>

                        {/* Video Call Interface */}
                        {isVideoCallActive && (
                          <>
                            {/* Remote video (main view) */}
                            <Box sx={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              right: 0,
                              bottom: 0,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}>
                              <video
                                ref={(ref) => setRemoteVideoRef(ref)}
                                autoPlay
                                playsInline
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover',
                                  backgroundColor: 'rgba(0,0,0,0.8)'
                                }}
                              />
                            </Box>

                            {/* Local video (self-view) */}
                            <Box sx={{
                              position: 'absolute',
                              top: 20,
                              right: 20,
                              width: 200,
                              height: 150,
                              borderRadius: 2,
                              border: '2px solid rgba(255,255,255,0.3)',
                              overflow: 'hidden',
                              zIndex: 1001
                            }}>
                              <video
                                ref={(ref) => setLocalVideoRef(ref)}
                                autoPlay
                                playsInline
                                muted
                                style={{
                                  width: '100%',
                                  height: '100%',
                                  objectFit: 'cover'
                                }}
                              />
                            </Box>
                          </>
                        )}

                        {/* Call Duration */}
                        <Box sx={{
                          position: 'absolute',
                          top: 20,
                          left: 20,
                          bgcolor: 'rgba(0,0,0,0.6)',
                          px: 3,
                          py: 1.5,
                          borderRadius: 3,
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255,255,255,0.1)',
                          boxShadow: '0 4px 15px rgba(0,0,0,0.3)'
                        }}>
                          <Typography variant="body2" sx={{
                            fontWeight: 600,
                            fontSize: '0.95rem',
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                          }}>
                            <Box sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: 'error.main',
                              animation: 'blink 1s ease-in-out infinite'
                            }} />
                            00:45
                          </Typography>
                        </Box>

                        {/* Call Controls */}
                        <Box sx={{
                          display: 'flex',
                          gap: 2,
                          mt: 4,
                          bgcolor: 'rgba(0,0,0,0.4)',
                          p: 3,
                          borderRadius: 4,
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255,255,255,0.1)',
                          boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
                          zIndex: 1
                        }}>
                          {/* Mute/Unmute Button */}
                          <Box sx={{ textAlign: 'center' }}>
                            <IconButton
                              onClick={toggleMute}
                              sx={{
                                bgcolor: isMuted ? 'error.main' : 'rgba(255,255,255,0.15)',
                                color: 'white',
                                width: 70,
                                height: 70,
                                mb: 1,
                                border: '2px solid rgba(255,255,255,0.2)',
                                '&:hover': {
                                  bgcolor: isMuted ? 'error.dark' : 'rgba(255,255,255,0.25)',
                                  transform: 'scale(1.1)',
                                  boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
                                },
                                transition: 'all 0.3s ease',
                                boxShadow: isMuted ? '0 0 20px rgba(244, 67, 54, 0.5)' : '0 4px 15px rgba(0,0,0,0.2)'
                              }}
                            >
                              {isMuted ? <MicOffIcon fontSize="large" /> : <MicIcon fontSize="large" />}
                            </IconButton>
                            <Typography variant="caption" sx={{
                              display: 'block',
                              color: 'rgba(255,255,255,0.8)',
                              fontWeight: 500
                            }}>
                              {isMuted ? 'Unmute' : 'Mute'}
                            </Typography>
                          </Box>

                          {/* Video Toggle (only for video calls) */}
                          {isVideoCallActive && (
                            <Box sx={{ textAlign: 'center' }}>
                              <IconButton
                                onClick={toggleVideo}
                                sx={{
                                  bgcolor: !isVideoEnabled ? 'error.main' : 'rgba(255,255,255,0.15)',
                                  color: 'white',
                                  width: 70,
                                  height: 70,
                                  mb: 1,
                                  border: '2px solid rgba(255,255,255,0.2)',
                                  '&:hover': {
                                    bgcolor: !isVideoEnabled ? 'error.dark' : 'rgba(255,255,255,0.25)',
                                    transform: 'scale(1.1)',
                                    boxShadow: '0 4px 20px rgba(0,0,0,0.3)'
                                  },
                                  transition: 'all 0.3s ease',
                                  boxShadow: !isVideoEnabled ? '0 0 20px rgba(244, 67, 54, 0.5)' : '0 4px 15px rgba(0,0,0,0.2)'
                                }}
                              >
                                {isVideoEnabled ? <VideocamIcon fontSize="large" /> : <VideocamOffIcon fontSize="large" />}
                              </IconButton>
                              <Typography variant="caption" sx={{
                                display: 'block',
                                color: 'rgba(255,255,255,0.8)',
                                fontWeight: 500
                              }}>
                                {isVideoEnabled ? 'Stop Video' : 'Start Video'}
                              </Typography>
                            </Box>
                          )}

                          {/* End Call Button */}
                          <Box sx={{ textAlign: 'center' }}>
                            <IconButton
                              onClick={handleEndCall}
                              sx={{
                                bgcolor: 'error.main',
                                color: 'white',
                                width: 70,
                                height: 70,
                                mb: 1,
                                border: '2px solid rgba(255,255,255,0.2)',
                                '&:hover': {
                                  bgcolor: 'error.dark',
                                  transform: 'scale(1.1)',
                                  boxShadow: '0 4px 25px rgba(244, 67, 54, 0.6)'
                                },
                                transition: 'all 0.3s ease',
                                boxShadow: '0 0 25px rgba(244, 67, 54, 0.4)'
                              }}
                            >
                              <CallEndIcon fontSize="large" />
                            </IconButton>
                            <Typography variant="caption" sx={{
                              display: 'block',
                              color: 'rgba(255,255,255,0.8)',
                              fontWeight: 500
                            }}>
                              End Call
                            </Typography>
                          </Box>
                        </Box>

                        {/* Call Quality Indicator */}
                        <Box sx={{
                          position: 'absolute',
                          bottom: 20,
                          left: 20,
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1.5,
                          bgcolor: 'rgba(0,0,0,0.6)',
                          px: 3,
                          py: 1.5,
                          borderRadius: 3,
                          backdropFilter: 'blur(10px)',
                          border: '1px solid rgba(255,255,255,0.1)',
                          boxShadow: '0 4px 15px rgba(0,0,0,0.3)'
                        }}>
                          {/* Signal Strength Bars */}
                          <Box sx={{ display: 'flex', alignItems: 'end', gap: 0.5 }}>
                            <Box sx={{
                              width: 3,
                              height: 8,
                              bgcolor: 'success.main',
                              borderRadius: 1,
                              animation: 'blink 2s ease-in-out infinite'
                            }} />
                            <Box sx={{
                              width: 3,
                              height: 12,
                              bgcolor: 'success.main',
                              borderRadius: 1,
                              animation: 'blink 2s ease-in-out infinite 0.2s'
                            }} />
                            <Box sx={{
                              width: 3,
                              height: 16,
                              bgcolor: 'success.main',
                              borderRadius: 1,
                              animation: 'blink 2s ease-in-out infinite 0.4s'
                            }} />
                            <Box sx={{
                              width: 3,
                              height: 20,
                              bgcolor: 'success.main',
                              borderRadius: 1,
                              animation: 'blink 2s ease-in-out infinite 0.6s'
                            }} />
                          </Box>
                          <Typography variant="caption" sx={{
                            fontWeight: 600,
                            fontSize: '0.85rem'
                          }}>
                            Excellent Quality
                          </Typography>
                        </Box>
                      </Box>
                    )}

                    {/* Messages Area */}
                    {chatLoading ? (
                      <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        <CircularProgress />
                      </Box>
                    ) : (
                      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2, backgroundColor: '#f5f5f5' }}>
                        <List>
                          {messages.map((message) => {
                            const isDoctor = message.senderRole === 'doctor';
                            const isDoctorMessage = message.senderId === userProfile?.uid;

                            return (
                              <ListItem
                                key={message.id}
                                sx={{
                                  textAlign: isDoctorMessage ? 'right' : 'left',
                                  mb: 2,
                                  px: 2
                                }}
                              >
                                <Grid container spacing={2} direction={isDoctorMessage ? 'row-reverse' : 'row'}>
                                  <Grid item xs={1}>
                                    <Avatar
                                      src={message.senderAvatar}
                                      sx={{
                                        bgcolor: isDoctorMessage ? 'primary.main' : 'secondary.main',
                                      }}
                                    >
                                      {isDoctorMessage
                                        ? (userProfile?.displayName?.charAt(0) || userProfile?.email?.charAt(0) || 'D').toUpperCase()
                                        : (message.senderName?.charAt(0) || selectedPatient?.name?.charAt(0) || 'P').toUpperCase()
                                      }
                                    </Avatar>
                                  </Grid>
                                  <Grid item xs={11} sm={8} md={7}>
                                    <Paper
                                      elevation={1}
                                      sx={{
                                        p: 2,
                                        bgcolor: isDoctorMessage ? 'primary.light' : 'white',
                                        color: isDoctorMessage ? 'white' : 'text.primary',
                                        borderRadius: isDoctorMessage
                                          ? '20px 20px 5px 20px'
                                          : '20px 20px 20px 5px',
                                        boxShadow: '0 2px 10px rgba(0,0,0,0.05)'
                                      }}
                                    >
                                      <Typography variant="body1">{message.content}</Typography>
                                      <Typography variant="caption" sx={{ display: 'block', mt: 1, textAlign: 'right', opacity: 0.8 }}>
                                        {message.timestamp?.toDate?.()?.toLocaleTimeString() || 'Now'}
                                      </Typography>
                                    </Paper>
                                  </Grid>
                                </Grid>
                              </ListItem>
                            );
                          })}
                          <div ref={messagesEndRef} />
                        </List>
                      </Box>
                    )}

                    {/* Message Input */}
                    <Box sx={{ p: 2, backgroundColor: 'background.paper', borderTop: '1px solid', borderColor: 'divider' }}>
                      <form onSubmit={handleSendMessage}>
                        <Grid container spacing={1}>
                          <Grid item xs={11}>
                            <TextField
                              fullWidth
                              placeholder="Type a message"
                              variant="outlined"
                              value={newMessage}
                              onChange={(e) => setNewMessage(e.target.value)}
                              size="small"
                              sx={{
                                '& .MuiOutlinedInput-root': {
                                  borderRadius: 2
                                }
                              }}
                            />
                          </Grid>
                          <Grid item xs={1}>
                            <Button
                              variant="contained"
                              color="primary"
                              type="submit"
                              disabled={!newMessage.trim()}
                              sx={{ minWidth: 0, p: 1, borderRadius: 2, height: '100%' }}
                            >
                              <SendIcon />
                            </Button>
                          </Grid>
                        </Grid>
                      </form>
                    </Box>
                  </>
                ) : (
                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '100%',
                    p: 3,
                    textAlign: 'center'
                  }}>
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Welcome to the Doctor Chat Portal
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                      Select a patient from the list to start a conversation
                    </Typography>
                    <img
                      src="/chat-illustration.svg"
                      alt="Chat illustration"
                      style={{ maxWidth: '60%', opacity: 0.7 }}
                    />
                  </Box>
                )}
              </Paper>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Layout>
  );
};

export default DoctorChat;

